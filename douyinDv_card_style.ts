// 抖音视频下载服务 - 卡片式布局版本
const pattern = /"video":{"play_addr":{"uri":"([a-z0-9]+)"/;
const cVUrl = "https://www.iesdouyin.com/aweme/v1/play/?video_id=%s&ratio=1080p&line=0";
const statsRegex = /"statistics"\s*:\s*\{([\s\S]*?)\},/;
const regex = /"nickname":\s*"([^"]+)",\s*"signature":\s*"([^"]+)"/;
const ctRegex = /"create_time":\s*(\d+)/;
const descRegex = /"desc":\s*"([^"]+)"/;

interface DouyinVideoInfo {
  aweme_id: string | null;
  comment_count: number | null;
  digg_count: number | null;
  share_count: number | null;
  collect_count: number | null;
  nickname: string | null;
  signature: string | null;
  desc: string | null;
  create_time: string | null;
  video_url: string | null;
  type: string | null;
  image_url_list: string[] | null;
}

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

async function doGet(url: string): Promise<Response> {
  const headers = new Headers();
  headers.set(
    "User-Agent",
    "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36",
  );
  const resp = await fetch(url, { method: "GET", headers });
  return resp;
}

async function getVideoInfo(url: string): Promise<DouyinVideoInfo> {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  
  const video_url = cVUrl.replace("%s", match[1]);
  const auMatch = body.match(regex);
  const ctMatch = body.match(ctRegex);
  const descMatch = body.match(descRegex);
  const statsMatch = body.match(statsRegex);
  
  if (statsMatch) {
    const innerContent = statsMatch[0];
    const awemeIdMatch = innerContent.match(/"aweme_id"\s*:\s*"([^"]+)"/);
    const commentCountMatch = innerContent.match(/"comment_count"\s*:\s*(\d+)/);
    const diggCountMatch = innerContent.match(/"digg_count"\s*:\s*(\d+)/);
    const shareCountMatch = innerContent.match(/"share_count"\s*:\s*(\d+)/);
    const collectCountMatch = innerContent.match(/"collect_count"\s*:\s*(\d+)/);
    
    const douyinVideoInfo: DouyinVideoInfo = {
      aweme_id: awemeIdMatch ? awemeIdMatch[1] : null,
      comment_count: commentCountMatch ? parseInt(commentCountMatch[1]) : null,
      digg_count: diggCountMatch ? parseInt(diggCountMatch[1]) : null,
      share_count: shareCountMatch ? parseInt(shareCountMatch[1]) : null,
      collect_count: collectCountMatch ? parseInt(collectCountMatch[1]) : null,
      nickname: null,
      signature: null,
      desc: null,
      create_time: null,
      video_url: video_url,
      type: "video",
      image_url_list: null
    };
    
    if (auMatch) {
      douyinVideoInfo.nickname = auMatch[1];
      douyinVideoInfo.signature = auMatch[2];
    }
    if (ctMatch) {
      const date = new Date(parseInt(ctMatch[1]) * 1000);
      douyinVideoInfo.create_time = formatDate(date);
    }
    if (descMatch) {
      douyinVideoInfo.desc = descMatch[1];
    }
    
    return douyinVideoInfo;
  } else {
    throw new Error("No stats found in the response.");
  }
}

async function getVideoUrl(url: string): Promise<string> {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  return cVUrl.replace("%s", match[1]);
}

// 生成卡片式布局的HTML页面
function generateHTML(): string {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音视频下载工具 - 卡片式布局</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 页面标题区域 */
        .title-section {
            text-align: center;
            margin-bottom: 32px;
        }

        .title-section h1 {
            font-size: 2.8rem;
            color: #ffffff;
            margin-bottom: 12px;
            font-weight: 700;
            letter-spacing: -0.5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .title-section p {
            color: #f8f9fa;
            font-size: 1.2rem;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .title-section .subtitle {
            color: #e9ecef;
            font-size: 0.95rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        /* 主要内容区域 */
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 32px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 卡片网格布局 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        /* 基础卡片样式 - 现代化设计 */
        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 24px;
            transition: all 0.3s ease;
            position: relative;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f1f3f4;
        }

        .card-icon {
            font-size: 1.2rem;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #5a6c7d;
            font-size: 0.95rem;
        }

        .url-input {
            width: 100%;
            padding: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            resize: none;
            font-family: inherit;
            line-height: 1.5;
            min-height: 120px;
        }

        .url-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
            transform: translateY(-1px);
        }

        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            min-width: 140px;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #e081e9 0%, #e3455a 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 加载卡片 */
        .loading-card {
            display: none;
            text-align: center;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid rgba(102, 126, 234, 0.3);
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid rgba(102, 126, 234, 0.2);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 结果展示区域 */
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .result-card {
            /* 基础结果卡片样式，具体配色由子类定义 */
        }

        /* 多元化详情卡片配色 */
        .info-card-basic {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.08) 0%, rgba(155, 89, 182, 0.08) 100%);
        }

        .info-card-author {
            background: linear-gradient(135deg, rgba(230, 126, 34, 0.08) 0%, rgba(231, 76, 60, 0.08) 100%);
        }

        .info-card-stats {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.08) 0%, rgba(26, 188, 156, 0.08) 100%);
        }

        .info-item {
            display: flex;
            align-items: flex-start;
            padding: 10px 0;
            border-bottom: 1px solid #f5f6f7;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #5a6c7d;
            min-width: 90px;
            flex-shrink: 0;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .info-value {
            color: #2c3e50;
            word-break: break-all;
            flex: 1;
            margin-left: 16px;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .info-value code {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
            color: #495057;
            border: 1px solid #e9ecef;
        }

        .copy-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .copy-btn:hover {
            background: linear-gradient(135deg, #218838 0%, #1ba085 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }

        /* 输入功能卡片 */
        .input-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
        }

        /* 使用说明卡片 */
        .guide-card {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.08) 0%, rgba(32, 201, 151, 0.08) 100%);
        }

        .guide-list {
            list-style: none;
            padding: 0;
        }

        .guide-list li {
            margin-bottom: 12px;
            padding-left: 24px;
            position: relative;
            color: #555;
        }

        .guide-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #17a2b8;
            font-weight: bold;
            font-size: 16px;
        }

        /* Toast通知 */
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            font-size: 14px;
            max-width: 300px;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.toast-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .toast.toast-error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .toast-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                min-width: auto;
            }
            
            .title-card h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题区域 -->
        <div class="title-section">
            <h1>🎵 抖音视频下载工具</h1>
            <p>快速获取无水印视频链接和详细信息</p>
            <div class="subtitle">现代化卡片式布局 · 简洁高效</div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="cards-grid">
                <!-- 输入功能卡片 -->
                <div class="card input-card">
                    <div class="card-title">视频链接输入</div>
                    <div class="input-group">
                        <textarea
                            id="videoUrl"
                            class="url-input"
                            placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本"
                            rows="3"
                        ></textarea>
                    </div>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="getVideoUrl()">
                            获取视频链接
                        </button>
                        <button class="btn btn-secondary" onclick="getVideoInfo()">
                            获取详细信息
                        </button>
                    </div>
                </div>

                <!-- 使用说明卡片 -->
                <div class="card guide-card">
                    <div class="card-title">使用说明</div>
                    <ul class="guide-list">
                        <li>支持抖音APP分享的短链接（如：https://v.douyin.com/xxxx/）</li>
                        <li>可以直接粘贴抖音分享的完整文本，系统会自动提取链接</li>
                        <li>"获取视频链接"返回可直接下载的无水印视频地址</li>
                        <li>"获取详细信息"返回视频的完整元数据信息</li>
                        <li>点击复制按钮可快速复制结果到剪贴板</li>
                    </ul>
                </div>
            </div>

            <!-- 加载状态卡片 - 移到独立位置 -->
            <div class="card loading-card" id="loadingCard">
                <div class="card-title">正在处理</div>
                <div class="spinner"></div>
                <p>正在解析视频信息，请稍候...</p>
            </div>

            <!-- 结果展示区域 -->
            <div class="results-grid" id="resultsGrid" style="display: none;">
                <!-- 结果卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        console.log('卡片式布局页面已加载');

        // 提取URL的正则表达式
        const urlRegex = /https?:\\/\\/v\\.douyin\\.com\\/[A-Za-z0-9]+\\/?/;

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingCard').style.display = 'block';
            document.getElementById('resultsGrid').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingCard').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }

        // 显示Toast通知
        function showToast(message, type) {
            if (!type) type = 'success';
            const toast = document.createElement('div');
            toast.className = 'toast toast-' + type;

            const toastContent = document.createElement('div');
            toastContent.className = 'toast-content';

            const toastIcon = document.createElement('span');
            toastIcon.textContent = type === 'success' ? '✅' : '❌';

            const toastMessage = document.createElement('span');
            toastMessage.textContent = message;

            toastContent.appendChild(toastIcon);
            toastContent.appendChild(toastMessage);
            toast.appendChild(toastContent);
            document.body.appendChild(toast);

            setTimeout(function() {
                toast.classList.add('show');
            }, 100);

            setTimeout(function() {
                toast.classList.remove('show');
                setTimeout(function() {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 复制到剪贴板
        function copyToClipboard(text, buttonElement) {
            try {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        if (buttonElement) {
                            const originalText = buttonElement.textContent;
                            const originalBg = buttonElement.style.backgroundColor;
                            buttonElement.textContent = '✅ 已复制';
                            buttonElement.style.backgroundColor = '#28a745';

                            setTimeout(function() {
                                buttonElement.textContent = originalText;
                                buttonElement.style.backgroundColor = originalBg;
                            }, 2000);
                        }
                        showToast('链接已复制到剪贴板！');
                    }).catch(function(err) {
                        showToast('复制失败，请手动复制', 'error');
                    });
                } else {
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    if (buttonElement) {
                        const originalText = buttonElement.textContent;
                        const originalBg = buttonElement.style.backgroundColor;
                        buttonElement.textContent = '✅ 已复制';
                        buttonElement.style.backgroundColor = '#28a745';

                        setTimeout(function() {
                            buttonElement.textContent = originalText;
                            buttonElement.style.backgroundColor = originalBg;
                        }, 2000);
                    }
                    showToast('链接已复制到剪贴板！');
                }
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
        }

        // 提取并验证URL
        function extractAndValidateUrl() {
            const input = document.getElementById('videoUrl').value.trim();
            if (!input) {
                showToast('请输入抖音视频链接', 'error');
                return null;
            }

            const match = input.match(urlRegex);
            if (match) {
                return match[0];
            }

            if (input.startsWith('http')) {
                return input;
            }

            showToast('未找到有效的抖音视频链接，请检查输入格式', 'error');
            return null;
        }

        // 显示结果卡片
        function showResults(cards) {
            const resultsGrid = document.getElementById('resultsGrid');
            resultsGrid.innerHTML = '';

            cards.forEach(function(cardData) {
                const card = document.createElement('div');
                card.className = 'card result-card ' + (cardData.cardClass || '');

                const cardTitle = document.createElement('div');
                cardTitle.className = 'card-title';
                cardTitle.innerHTML = '<span class="card-icon">' + cardData.icon + '</span>' + cardData.title;

                const cardContent = document.createElement('div');
                cardContent.innerHTML = cardData.content;

                card.appendChild(cardTitle);
                card.appendChild(cardContent);
                resultsGrid.appendChild(card);
            });

            resultsGrid.style.display = 'grid';
        }

        // 获取视频链接
        function getVideoUrl() {
            const url = extractAndValidateUrl();
            if (!url) return;

            showLoading();

            fetch('?url=' + encodeURIComponent(url))
                .then(function(response) {
                    return response.text();
                })
                .then(function(result) {
                    hideLoading();

                    const cards = [{
                        icon: '🎬',
                        title: '视频下载链接',
                        cardClass: 'info-card-basic',
                        content:
                            '<div class="info-item">' +
                                '<span class="info-label">下载地址：</span>' +
                                '<span class="info-value">' +
                                    '<code style="font-size: 12px; background: #f8f9fa; padding: 4px 8px; border-radius: 4px; word-break: break-all;">' + result + '</code>' +
                                    '<button class="copy-btn" onclick="copyToClipboard(\\'' + result + '\\', this)">复制链接</button>' +
                                '</span>' +
                            '</div>' +
                            '<div style="margin-top: 16px; padding: 12px; background: #e3f2fd; border-radius: 8px; font-size: 14px; color: #1976d2;">' +
                                '💡 提示：点击复制按钮复制链接，然后在浏览器中打开即可下载视频' +
                            '</div>'
                    }];

                    showResults(cards);
                })
                .catch(function(error) {
                    hideLoading();
                    showToast('网络请求失败，请检查网络连接: ' + error.message, 'error');
                });
        }

        // 获取视频详细信息
        function getVideoInfo() {
            const url = extractAndValidateUrl();
            if (!url) return;

            showLoading();

            fetch('?data&url=' + encodeURIComponent(url))
                .then(function(response) {
                    return response.text();
                })
                .then(function(result) {
                    hideLoading();

                    try {
                        const videoInfo = JSON.parse(result);

                        const cards = [
                            {
                                icon: '📋',
                                title: '基本信息',
                                cardClass: 'info-card-basic',
                                content:
                                    '<div class="info-item">' +
                                        '<span class="info-label">视频ID：</span>' +
                                        '<span class="info-value">' + (videoInfo.aweme_id || '未知') + '</span>' +
                                    '</div>' +
                                    '<div class="info-item">' +
                                        '<span class="info-label">标题：</span>' +
                                        '<span class="info-value">' + (videoInfo.desc || '无标题') + '</span>' +
                                    '</div>' +
                                    '<div class="info-item">' +
                                        '<span class="info-label">创建时间：</span>' +
                                        '<span class="info-value">' + (videoInfo.create_time || '未知') + '</span>' +
                                    '</div>'
                            },
                            {
                                icon: '👤',
                                title: '作者信息',
                                cardClass: 'info-card-author',
                                content:
                                    '<div class="info-item">' +
                                        '<span class="info-label">作者：</span>' +
                                        '<span class="info-value">' + (videoInfo.nickname || '未知') + '</span>' +
                                    '</div>' +
                                    '<div class="info-item">' +
                                        '<span class="info-label">签名：</span>' +
                                        '<span class="info-value">' + (videoInfo.signature || '无签名') + '</span>' +
                                    '</div>'
                            },
                            {
                                icon: '📊',
                                title: '互动数据',
                                cardClass: 'info-card-stats',
                                content:
                                    '<div class="info-item">' +
                                        '<span class="info-label">点赞数：</span>' +
                                        '<span class="info-value">' + ((videoInfo.digg_count && videoInfo.digg_count.toLocaleString()) || '0') + '</span>' +
                                    '</div>' +
                                    '<div class="info-item">' +
                                        '<span class="info-label">评论数：</span>' +
                                        '<span class="info-value">' + ((videoInfo.comment_count && videoInfo.comment_count.toLocaleString()) || '0') + '</span>' +
                                    '</div>' +
                                    '<div class="info-item">' +
                                        '<span class="info-label">分享数：</span>' +
                                        '<span class="info-value">' + ((videoInfo.share_count && videoInfo.share_count.toLocaleString()) || '0') + '</span>' +
                                    '</div>' +
                                    '<div class="info-item">' +
                                        '<span class="info-label">收藏数：</span>' +
                                        '<span class="info-value">' + ((videoInfo.collect_count && videoInfo.collect_count.toLocaleString()) || '0') + '</span>' +
                                    '</div>'
                            }
                        ];

                        showResults(cards);
                    } catch (parseError) {
                        showToast('响应格式错误: ' + result, 'error');
                    }
                })
                .catch(function(error) {
                    hideLoading();
                    showToast('解析失败，请检查链接是否正确: ' + error.message, 'error');
                });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');

            // 回车键支持
            const urlInput = document.getElementById('videoUrl');
            if (urlInput) {
                urlInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        getVideoUrl();
                    }
                });

                // 自动聚焦输入框
                urlInput.focus();
            }
        });
    </script>
</body>
</html>
  `;
}

const handler = async (req: Request) => {
  console.log("Method:", req.method);
  const url = new URL(req.url);

  // 设置CORS头
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type",
  };

  // 处理OPTIONS请求
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  if (url.searchParams.has("url")) {
    const inputUrl = url.searchParams.get("url")!;
    console.log("inputUrl:", inputUrl);

    try {
      // 返回完整json数据
      if (url.searchParams.has("data")) {
        const videoInfo = await getVideoInfo(inputUrl);
        return new Response(JSON.stringify(videoInfo), {
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders
          }
        });
      }

      const videoUrl = await getVideoUrl(inputUrl);
      return new Response(videoUrl, {
        headers: corsHeaders
      });
    } catch (error) {
      return new Response(`错误: ${error.message}`, {
        status: 500,
        headers: corsHeaders
      });
    }
  } else {
    // 返回卡片式布局的HTML页面
    return new Response(generateHTML(), {
      headers: {
        "Content-Type": "text/html; charset=utf-8",
        ...corsHeaders
      }
    });
  }
};

Deno.serve({ port: 8082 }, handler);
